﻿#ifndef MONITORLIST_H
#define MONITORLIST_H
#include <QxOrm.h>
#include <QString>

class MonitorList
{
public:
    long id;                   // ID
    QString settingName;       // 配置名称
    QString tableName;         // 表(组)名称，监视表中有多个组，一个组包含多个变量
    long varID;                // 变量表变量索引
    QString monitorName;       // 监视表名称
    QString monitoredValue;    // 监视值
    QString displayFormat;     // 显示格式
    int monitoringWithTrigger; // 使用触发器监视
    int modifyWithTrigger;     // 使用触发器修改
    QString modifyValue;       // 修改值
    bool isModify;             // 是否修改
    QString description;       // 注释
    int state;                 // 状态
    QString scope;          // 变量作用域
    QString owned;          // 文件名
    QString type;           // 文件分类
    QString dataType;       // 数据类型
    long dataTypeID;       // 数据类型ID
    MonitorList() {};
};
QX_REGISTER_HPP_EXPORT_DLL(MonitorList, qx::trait::no_base_class_defined, 0)

#endif // MONITORLIST_H
